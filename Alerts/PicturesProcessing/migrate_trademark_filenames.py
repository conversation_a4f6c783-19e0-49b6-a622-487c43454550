#!/usr/bin/env python3
"""
Migration script to convert trademark filenames from reg_no based to ser_no based naming.
Converts {reg_no}.webp to {ser_no}.webp and {reg_no}_full.webp to {ser_no}_full.webp

This script:
1. Processes all cases with trademark images in the dataframe
2. Converts reg_no to ser_no using the trademark database
3. Uses LLM to identify missing reg/ser numbers if needed
4. Prompts user for manual input when automatic methods fail
5. Updates database records (tro=TRUE, plaintiff_id)
6. Copies files to new names using async_copy_file_with_retry
7. Updates dataframe with new filenames and ser_no information
"""

import os, sys, re, asyncio, json, copy, traceback, requests
from typing import Dict, Optional, Tuple
sys.path.append(os.getcwd())
import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from IP.Trademarks_Bulk.trademark_db import get_db_connection
from FileManagement.Tencent_COS import get_cos_client, async_copy_file_with_retry
from AI.GC_VertexAI import vertex_genai_multi_async
from Common import Constants
from Common.Constants import local_ip_folder
from IP.Trademarks.USPTO_TSDR_API import TSDRApi
from IP.Trademarks.Trademark_API import get_certificate_local_path
from Alerts.ReprocessCases import reprocess_cases
import csv
from datetime import datetime

USER_ANSWERS_CACHE_FILE = os.path.join(os.path.dirname(__file__), "user_answers_cache.json")
CASES_TO_REPROCESS_FILE = os.path.join(os.path.dirname(__file__), "cases_to_reprocess.csv")
CHANGES_LOG_FILE = os.path.join(os.path.dirname(__file__), "migration_changes_log.csv")
CHANGES_SUMMARY_FILE = os.path.join(os.path.dirname(__file__), "migration_changes_summary.json")
LOG_FILE = os.path.join(os.path.dirname(__file__), f"migration_console_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")


class TeeOutput:
    """Write to both console and file simultaneously"""
    def __init__(self, file_path):
        self.terminal = sys.stdout
        self.log = open(file_path, 'w', encoding='utf-8')

    def write(self, message):
        self.terminal.write(message)  # Still show on console
        self.log.write(message)       # Also write to file
        self.log.flush()              # Ensure immediate write

    def flush(self):
        self.terminal.flush()
        self.log.flush()

    def close(self):
        self.log.close()


class TrademarkFilenameMigrator:
    def __init__(self):
        self.db_conn = None
        self.cos_client = None
        self.cos_bucket = None
        self.tsdr_api = None
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.user_answers_cache: Dict[Tuple[int, str], str] = self._load_user_answers_cache()
        self.cases_to_reprocess = self._load_cases_to_reprocess()
        self.cases_df = None
        self.plaintiffs_df = None
        self.changes_log = []  # Track all changes
        self.changes_summary = {
            'total_cases_processed': 0,
            'cases_with_changes': 0,
            'total_trademarks_changed': 0,
            'total_files_to_copy': 0,
            'database_updates_needed': 0,
            'changes_by_type': {
                'filename_changes': 0,
                'ser_no_additions': 0,
                'trademark_removals': 0,
                'dead_trademark_markings': 0,
                'mark_text_additions': 0
            }
        }
    
    def log_change(self, case_id: int, plaintiff_id: int, change_type: str, 
                   old_value: str, new_value: str, details: str = ""):
        """Log a single change for tracking"""

        change_record = {
            'timestamp': datetime.now().isoformat(),
            'case_id': case_id,
            'plaintiff_id': plaintiff_id,
            'change_type': change_type,
            'old_value': old_value,
            'new_value': new_value,
            'details': details
        }
        self.changes_log.append(change_record)
        
        # Update summary counters
        if change_type in self.changes_summary['changes_by_type']:
            self.changes_summary['changes_by_type'][change_type] += 1

    def save_changes_log(self):
        """Save all changes to CSV file"""

        if not self.changes_log:
            print("No changes to save.")
            return
            
        with open(CHANGES_LOG_FILE, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=[
                'timestamp', 'case_id', 'plaintiff_id', 'change_type', 
                'old_value', 'new_value', 'details'
            ])
            writer.writeheader()
            writer.writerows(self.changes_log)
        
        print(f"Changes log saved to {CHANGES_LOG_FILE}")

    def save_changes_summary(self):
        """Save summary statistics to JSON file"""

        with open(CHANGES_SUMMARY_FILE, 'w') as f:
            json.dump(self.changes_summary, f, indent=4)
        
        print(f"Changes summary saved to {CHANGES_SUMMARY_FILE}")

    def print_changes_summary(self):
        """Print a summary of all changes"""

        print(f"\n{'='*60}")
        print("MIGRATION CHANGES SUMMARY")
        print(f"{'='*60}")
        print(f"Total cases processed: {self.changes_summary['total_cases_processed']}")
        print(f"Cases with changes: {self.changes_summary['cases_with_changes']}")
        print(f"Total trademarks changed: {self.changes_summary['total_trademarks_changed']}")
        print(f"Total files to copy: {self.changes_summary['total_files_to_copy']}")
        print(f"Database updates needed: {self.changes_summary['database_updates_needed']}")
        
        print(f"\nChanges by type:")
        for change_type, count in self.changes_summary['changes_by_type'].items():
            if count > 0:
                print(f"  - {change_type}: {count}")
        
        print(f"\nDetailed changes logged to: {CHANGES_LOG_FILE}")
        print(f"Summary saved to: {CHANGES_SUMMARY_FILE}")

    async def __aenter__(self):
        self.db_conn = get_db_connection()
        self.cos_client, self.cos_bucket = get_cos_client()
        self.tsdr_api = TSDRApi()
        await self.tsdr_api.start_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()
        if self.tsdr_api:
            await self.tsdr_api.close_session()

    def __enter__(self):
        self.db_conn = get_db_connection()
        self.cos_client, self.cos_bucket = get_cos_client()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()
    
    def _load_user_answers_cache(self) -> Dict[Tuple[int, str], str]:
        """Loads the user answers cache from a local file."""
        if os.path.exists(USER_ANSWERS_CACHE_FILE):
            with open(USER_ANSWERS_CACHE_FILE, 'r') as f:
                try:
                    # Keys from JSON are strings, convert them back to (int, str) tuple
                    loaded_cache = json.load(f)
                    return {tuple(k.split('_', 1)): v for k, v in loaded_cache.items()}
                except json.JSONDecodeError as e:
                    print(f"Error decoding user answers cache file: {e}")
                    return {}
        return {}

    def _save_user_answers_cache(self):
        """Saves the user answers cache to a local file."""
        with open(USER_ANSWERS_CACHE_FILE, 'w') as f:
            # Convert tuple keys to string keys for JSON serialization
            json.dump({f"{k[0]}_{k[1]}": v for k, v in self.user_answers_cache.items()}, f, indent=4)
        print(f"User answers cache saved to {USER_ANSWERS_CACHE_FILE}")

    def _load_cases_to_reprocess(self) -> list[int]:
        """Loads the list of cases to reprocess from a local CSV file."""
        if not os.path.exists(CASES_TO_REPROCESS_FILE):
            return []
        try:
            with open(CASES_TO_REPROCESS_FILE, 'r') as f:
                # Read, remove newline, convert to int, filter out empty lines
                return [int(case_id.strip()) for case_id in f if case_id.strip()]
        except (IOError, ValueError) as e:
            print(f"Error loading cases to reprocess file: {e}")
            return []

    def _save_case_to_reprocess(self, case_id: int):
        """Saves a case ID to the reprocess list and file if not already present."""
        if case_id not in self.cases_to_reprocess:
            self.cases_to_reprocess.append(case_id)
            try:
                with open(CASES_TO_REPROCESS_FILE, 'a') as f:
                    f.write(f"{case_id}\n")
                print(f"⚠️⚠️⚠️ Added case {case_id} to reprocess list. Now there are {len(self.cases_to_reprocess)} cases to reprocess.")
            except IOError as e:
                print(f"Error saving case {case_id} to reprocess file: {e}")
    
    def get_cases_to_reprocess(self):
        """Returns the unique list of cases that need reprocessing."""
        return list(set(self.cases_to_reprocess))

    def is_already_migrated(self, filename: str) -> bool:
        """Check if filename is already in ser_no format (8 digits)"""
        base_name = os.path.splitext(filename)[0]
        if base_name.endswith('_full'):
            base_name = base_name[:-5]  # Remove '_full'
        
        # Check if it's exactly 8 digits (ser_no format)
        return bool(re.match(r'^\d{8}$', base_name))
    
    def get_ser_no_from_reg_no(self, reg_no: str) -> Tuple[Optional[str], Optional[str]]:
        """Query database to get ser_no from reg_no"""
        if not reg_no:
            return None, None
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("SELECT ser_no, mark_text FROM trademarks WHERE reg_no = %s", (reg_no,))
            result = cursor.fetchone()
            if result and result[0]:
                return result[0], result[1] # Return ser_no and mark_text
            return None, None
        except Exception as e:
            print(f"Database error getting ser_no and mark_text for reg_no {reg_no}: {e}")
            return None, None
        
    def get_ser_no_from_ser_no(self, ser_no: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """Query database to get ser_no and mark_text from ser_no"""
        if not ser_no:
            return None, None, None
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("SELECT ser_no, reg_no, mark_text FROM trademarks WHERE ser_no = %s", (ser_no,))
            result = cursor.fetchone()
            if result and result[0]:
                return result[0], result[1], result[2] # Return ser_no and mark_text
            else:
                print(f"Ser_no {ser_no} not found in database")
            return None, None, None
        except Exception as e:
            print(f"Database error getting ser_no and mark_text for ser_no {ser_no}: {e}")
            return None, None, None

    async def check_if_trademark_is_dead(self, reg_no: str, force_fetch: bool = False) -> Tuple[bool, Optional[str], Optional[str], Optional[str]]:
        """
        Check if trademark is dead/cancelled by querying USPTO and checking status code.
        Returns (is_dead, ser_no, reg_no, mark_text) tuple.
        """
        dead_status_codes = ["600", "601", "602", "603", "604", "605", "606", "607", "608", "609",
                           "614", "618", "620", "622", "626", "710", "711", "712", "713", "714", "900"]

        if not reg_no:
            return False, None, None, None

        # Create XML folder path
        xml_folder = os.path.join(local_ip_folder, "Trademarks", "XML")
        os.makedirs(xml_folder, exist_ok=True)

        xml_file_path = os.path.join(xml_folder, f"{reg_no.zfill(7)}.xml")

        # Check if XML file already exists locally
        xml_content = None
        if os.path.exists(xml_file_path) and not force_fetch:
            print(f"Using existing XML file for reg_no {reg_no}")
            with open(xml_file_path, 'rb') as f:
                xml_content = f.read()
        else:
            # Get XML from USPTO
            print(f"Fetching XML from USPTO for reg_no {reg_no} (force_fetch={force_fetch})")
            xml_content = await self.tsdr_api.get_status_info_xml(reg_no, id_key='rn')

            if xml_content:
                # Save XML file locally
                with open(xml_file_path, 'wb') as f:
                    f.write(xml_content)
                print(f"Saved XML file for reg_no {reg_no}")

        if xml_content:
            # Process XML content to extract status code, ser_no, and text
            data = self.tsdr_api.process_xml_content(xml_content)
            status_code = data.get("status_code")
            ser_no = data.get("ser_no")
            reg_no_from_xml = data.get("reg_no")
            mark_text = data.get("text")

            print(f"Trademark {reg_no}: status_code={status_code}, ser_no={ser_no}")

            # Check if trademark is dead/cancelled
            is_dead = status_code in dead_status_codes if status_code else False

            return is_dead, ser_no, reg_no_from_xml, mark_text

        return False, None, None, None
    
    
    def download_image_locally(self, image_url: str, plaintiff_id: int, case_id: int) -> Optional[str]:
        """Downloads an image from a URL to a local path."""
        try:
            # Extract filename from URL
            filename = os.path.basename(image_url)
            
            if len(filename) == 13:  # 8 + .webp = 8 + 5 = 13
                local_path = get_certificate_local_path(local_ip_folder, ser_no=filename[:-5])
                if os.path.exists(local_path):
                    print(f"Image already exists locally: {local_path}")
                    return local_path
            else:
                # Define local directory path: local_ip_folder/Trademarks/Certificates/plaintiff_id/
                local_dir = os.path.join(local_ip_folder, "Trademarks", "Certificates", str(plaintiff_id))
                os.makedirs(local_dir, exist_ok=True)
                local_path = os.path.join(local_dir, filename)

                if os.path.exists(local_path):
                    print(f"Image already exists locally: {local_path}")
                    return local_path

            # print(f"Downloading image from {image_url} to {local_path}")
            response = requests.get(image_url, stream=True)
            response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # print(f"Successfully downloaded image to {local_path}")
            return local_path
        except requests.exceptions.RequestException as e:
            print(f"Error downloading image from {image_url}: {e}")
            self._save_case_to_reprocess(case_id)
            return None
        except Exception as e:
            print(f"An unexpected error occurred during image download for {image_url}: {e}")
            return None

    async def ask_llm_for_numbers(self, local_image_path: str, reg_no: str) -> Tuple[Optional[str], Optional[str]]:
        """Use LLM to identify reg_no and ser_no from certificate image"""
        try:
            prompt = f"""
            Looking at this trademark certificate image, please identify:
            1. The registration number (usually 6-7 digits), somtimes refered to as Reg. No.
            2. The serial number (usually 8 digits), sometimes refered to as Ser. No.
            
            The current reg_no we have is: {reg_no}
            
            Please respond in this exact format:
            REG_NO: [number]
            SER_NO: [number]
            
            If you cannot find either number, respond with "NOT_FOUND" for that field.
            """
            
            prompt_list = [("text", prompt), ("image_path", local_image_path)] # Use image_path instead of image_url
            ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=Constants.IMAGE_MODEL_FREE_VERTEX)
            
            # Parse LLM response
            reg_match = re.search(r'REG_NO:\s*([0-9,\-\s]+|NOT_FOUND)', ai_answer)
            ser_match = re.search(r'SER_NO:\s*([0-9,\-\s]+|NOT_FOUND)', ai_answer)
            
            def extract_digits(match):
                if match and match.group(1) != 'NOT_FOUND':
                    return re.sub(r'\D', '', match.group(1))  # Remove all non-digit characters
                return None

            reg_no_found = extract_digits(reg_match)
            ser_no_found = extract_digits(ser_match)
            
            return reg_no_found, ser_no_found
            
        except Exception as e:
            print(f"LLM error for {local_image_path}: {e}")
            return None, None
    
    async def ask_user_for_ser_no(self, local_image_path: str, reg_no: str, plaintiff_id: int) -> Optional[str]:
        """Prompt user for ser_no with option to skip, using and updating a cache."""
        # Extract pagexxx from local_image_path
        filename = os.path.basename(local_image_path)
        page_match = re.search(r'(page\d+)', filename)
        page_key = page_match.group(1) if page_match else filename
        
        cache_key = (plaintiff_id, page_key)

        # Check cache first
        if cache_key in self.user_answers_cache:
            cached_answer = self.user_answers_cache[cache_key]
            print(f"Using cached answer for (plaintiff_id={plaintiff_id}, page={page_key}): {cached_answer}")
            if cached_answer == "SKIP":
                return None
            return cached_answer

        print(f"\n{'='*50}")
        print(f"MANUAL INPUT REQUIRED")
        print(f"{'='*50}")
        print(f"Registration Number: {reg_no}")
        print(f"Certificate path: {local_image_path}")
        print(f"{'='*50}")
        
        while True:
            user_input = input("Enter the 8-digit serial number (or 'skip' to skip, 'remove' to remove trademark, 'dead' to mark as dead): ").strip()
            
            if user_input.lower() == 'skip':
                self.user_answers_cache[cache_key] = "SKIP" # Cache skip action
                return None
            
            if user_input.lower() == 'remove':
                self.user_answers_cache[cache_key] = "REMOVE_TRADEMARK" # Cache remove action
                return "REMOVE_TRADEMARK" # Special value to indicate removal
            
            if user_input.lower() == 'dead':
                self.user_answers_cache[cache_key] = "DEAD_TRADEMARK" # Cache dead action
                return "DEAD_TRADEMARK" # Special value to indicate trademark is dead
            
            if re.match(r'^\d{8}$', user_input):
                self.user_answers_cache[cache_key] = user_input # Cache valid ser_no
                return user_input
            
            print("Please enter exactly 8 digits, 'skip', 'remove', or 'dead'")
            
    async def ask_user_for_ser_no2(self, local_image_path: str, reg_no: str, plaintiff_id: int) -> Optional[str]:
        
        prompt = "Is this how you would expect a USPTO issued trademark registration certificate to look like? Is this a USPTO issued trademark registration certificate? Answer with a simple 'yes' or 'no'."
        prompt_list = [("text", prompt), ("image_path", local_image_path)]
        ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE, useVertexAI=Constants.IMAGE_MODEL_FREE_VERTEX)
        if "yes" in ai_answer.lower():
            return "DEAD_TRADEMARK"
        elif "no" in ai_answer.lower(): # Remove
            return "REMOVE_TRADEMARK"
        else: # Skip
            return None
    
    def update_database_record(self, ser_no: str, plaintiff_id: int) -> bool:
        """Update database record with tro='TRUE' and plaintiff_id"""
        try:
            cursor = self.db_conn.cursor()
            # Check if the record exists before attempting to update
            cursor.execute("SELECT ser_no FROM trademarks WHERE ser_no = %s", (ser_no,))
            result = cursor.fetchone()

            if result:
                cursor.execute("""
                    UPDATE trademarks
                    SET tro = 'TRUE', plaintiff_id = %s
                    WHERE ser_no = %s
                """, (plaintiff_id, ser_no))
            else:
                print(f"Trademark with ser_no {ser_no} not found in database")
                return False

            self.db_conn.commit()
            return True
        except Exception as e:
            print(f"Database update error for ser_no {ser_no}: {e}")
            return False
    
    async def copy_file_to_new_name(self, old_filename: str, new_filename: str, plaintiff_id: int, case_id: int) -> bool:
        """
        Copy file from old name to new name using async_copy_file_with_retry.
        If copy fails, add the case_id to a list for later reprocessing.
        """
        try:
            # Copy low resolution if it exists
            if "_full" not in old_filename and "_cert" not in old_filename:
                old_key_low = f"plaintiff_images/{plaintiff_id}/low/{old_filename}"
                new_key_low = f"plaintiff_images/{plaintiff_id}/low/{new_filename}"
                await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key_low, old_key_low)

            # Copy high resolution
            old_key_high = f"plaintiff_images/{plaintiff_id}/high/{old_filename}"
            new_key_high = f"plaintiff_images/{plaintiff_id}/high/{new_filename}"
            await async_copy_file_with_retry(self.cos_client, self.cos_bucket, new_key_high, old_key_high)
            
            return True
        except Exception as e:
            print(f"File copy error for case {case_id} from {old_filename} to {new_filename}: {e}")
            self._save_case_to_reprocess(case_id)
            return False
    
    async def process_trademark_entry(self, trademark_data: Dict, plaintiff_id: int, case_id: int) -> Tuple[bool, Dict]:
        # print(f"\nEntry{'='*100}")
        """Process a single trademark entry and return updated data"""
        original_reg_nos = trademark_data.get('reg_no', [])
        original_full_filenames = trademark_data.get('full_filename', [])

        if not original_full_filenames:
            print(f"\nEntry for case_id {case_id} {'='*100}")
            print(f"Skipping trademark entry due to missing or mismatched reg_no/full_filename for for case_id {case_id}: {trademark_data}")
            return False, trademark_data
        
        if len(original_reg_nos) != len(original_full_filenames):
            print(f"\nEntry for case_id {case_id} {'='*100}")
            print(f"Ignoring reg_no due to mismatched reg_no/full_filename lengths for case_id {case_id}: {trademark_data}")
            original_reg_nos = []
            
        # Clean up all reg_no values: only keep digits and zfill to 7 digits
        processed_items_for_output = [] # List of (cleaned_reg_no, ser_no, new_full_filename) for successful items
        all_mark_texts = [] # To collect all mark_texts for finding the longest one

        for i in range(len(original_full_filenames)):
            if original_reg_nos:
                current_original_reg_no = original_reg_nos[i]
            else:
                current_original_reg_no = ""
            current_full_filename = original_full_filenames[i]
            ser_no = None
            mark_text = None # Initialize mark_text

            # Clean up current reg_no: only keep digits and zfill to 7 digits
            reg_no_for_processing = re.sub(r'\D', '', str(current_original_reg_no))
            if len(reg_no_for_processing) >= 5:
                reg_no_for_processing = reg_no_for_processing.zfill(7)
            else:
                reg_no_for_processing = "" # Treat as not found, will trigger LLM/user flow

            # Try to get ser_no and mark_text from database
            ser_no, mark_text = self.get_ser_no_from_reg_no(reg_no_for_processing)

            if not ser_no:
                print(f"\nNotFound for case_id {case_id} {'='*100}")
                
            # If not found in database, check if trademark is dead/cancelled    
            if not ser_no and reg_no_for_processing:
                # print(f"Checking if trademark {reg_no_for_processing} is dead/cancelled...")
                is_dead, dead_ser_no, dead_reg_no, dead_mark_text = await self.check_if_trademark_is_dead(reg_no_for_processing)

                if is_dead and dead_ser_no:
                    print(f"Trademark {reg_no_for_processing} for case_id {case_id} is dead/cancelled. Using ser_no {dead_ser_no} with _dead_full filename.")
                    ser_no = dead_ser_no
                    mark_text = dead_mark_text
                    reg_no_for_processing = dead_reg_no if dead_reg_no else reg_no_for_processing
                    # Use dead filename format
                    processed_items_for_output.append((reg_no_for_processing, ser_no, f"{ser_no}_dead_full.webp"))
                    if mark_text:
                        all_mark_texts.append(mark_text)
                    continue  # Skip the rest of the processing for this item

            if not ser_no:
                # Try LLM identification
                certificate_url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{int(float(plaintiff_id))}/high/{current_full_filename}"
                try:
                    local_image_path = self.download_image_locally(certificate_url, plaintiff_id, case_id)
                    if not local_image_path or not os.path.exists(local_image_path):
                        print(f"Image was not downloaded successfully from {certificate_url}")
                        continue  # Skip LLM processing if image isn't downloaded
                except Exception as e:
                    print(f"Error downloading image from {certificate_url}: {e}")
                    continue
                
                # Proceed to LLM only if image was successfully downloaded
                llm_reg_no, llm_ser_no = await self.ask_llm_for_numbers(local_image_path, reg_no_for_processing)
                
                if llm_ser_no:
                    ser_no, reg_no, mark_text = self.get_ser_no_from_ser_no(llm_ser_no)
                    if ser_no:
                        print(f"LLM found ser_no '{llm_ser_no}' for reg_no '{reg_no_for_processing}'")
                        reg_no_for_processing = reg_no
                    else:
                        print(f"LLM found ser_no '{llm_ser_no}' for reg_no '{reg_no_for_processing}' but it was not found in the database")
                        
                
                if not ser_no and llm_reg_no:
                    llm_reg_no = llm_reg_no.zfill(7)
                    ser_no, mark_text = self.get_ser_no_from_reg_no(llm_reg_no)
                    if ser_no:
                        print(f"LLM found reg_no '{llm_reg_no}' but no ser_no for reg_no '{reg_no_for_processing}'. We then looked it up in the database and found ser_no '{ser_no}'")
                    else:
                        print(f"LLM found reg_no '{llm_reg_no}' but no ser_no for reg_no '{reg_no_for_processing}'. We then looked it up but '{llm_reg_no}' was not found in the database")
                        # print(f"Checking if trademark {llm_reg_no} is dead/cancelled...")
                        is_dead, dead_ser_no, dead_reg_no, dead_mark_text = await self.check_if_trademark_is_dead(llm_reg_no)

                        if is_dead and dead_ser_no:
                            print(f"Trademark {reg_no_for_processing} (LLM reg_no {llm_reg_no}) is dead/cancelled. Using ser_no {dead_ser_no} with _dead_full filename.")
                            ser_no = dead_ser_no
                            mark_text = dead_mark_text
                            reg_no_for_processing = dead_reg_no if dead_reg_no else reg_no_for_processing
                            # Use dead filename format
                            processed_items_for_output.append((reg_no_for_processing, ser_no, f"{ser_no}_dead_full.webp"))
                            if mark_text:
                                all_mark_texts.append(mark_text)
                            continue  # Skip the rest of the processing for this item

                if not ser_no:
                    print(f"LLM could not find ser_no or reg_no for reg_no '{reg_no_for_processing}' => asking user for input")
                    # Ask user for manual input
                    ser_no = await self.ask_user_for_ser_no2(local_image_path, reg_no_for_processing, plaintiff_id)
                    if not ser_no: # User chose to skip this specific reg_no
                        print(f"User chose to skip for reg_no {reg_no_for_processing}. Keeping original entry for this specific reg_no.")
                        processed_items_for_output.append((current_original_reg_no, "", current_full_filename))
                        continue # Skip this specific reg_no/full_filename pair

            if ser_no == "REMOVE_TRADEMARK":
                print(f"User chose to remove trademark for reg_no {reg_no_for_processing}. This specific entry will be excluded.")
                continue # Do not add this item to the final lists
            
            if ser_no == "DEAD_TRADEMARK":
                print(f"User chose to mark trademark {reg_no_for_processing} as dead. Forcing USPTO check.")
                # Force fetch the latest XML from USPTO
                is_dead_check, dead_ser_no_api, dead_reg_no_api, dead_mark_text_api = await self.check_if_trademark_is_dead(reg_no_for_processing, force_fetch=True)
                
                if is_dead_check and dead_ser_no_api:
                    print(f"Trademark {reg_no_for_processing} confirmed dead by USPTO. Using ser_no {dead_ser_no_api} with _dead_full filename.")
                    ser_no = dead_ser_no_api
                    mark_text = dead_mark_text_api
                    reg_no_for_processing = dead_reg_no_api if dead_reg_no_api else reg_no_for_processing
                    processed_items_for_output.append((reg_no_for_processing, ser_no, f"{ser_no}_dead_full.webp"))
                    if mark_text:
                        all_mark_texts.append(mark_text)
                    continue # Skip the rest of the processing for this item
                else:
                    print(f"Trademark {reg_no_for_processing} marked as dead by user, but could not confirm with USPTO or retrieve ser_no. Skipping this entry.")
                    processed_items_for_output.append((current_original_reg_no, "", current_full_filename))
                    continue

            # Update database record for this ser_no
            # if not self.update_database_record(ser_no, plaintiff_id):
                # print(f"Failed to update database for ser_no {ser_no}. Keeping original entry for this specific reg_no.")
                # return False, trademark_data # Keep entire original entry if DB update fails

            # If successful, add to processed_items_for_output
            processed_items_for_output.append((reg_no_for_processing, ser_no, f"{ser_no}_full.webp"))
            if mark_text:
                all_mark_texts.append(mark_text)

        # After processing all individual reg_no/full_filename pairs
        # Track original counts before deduplication
        original_reg_nos = [item[0] for item in processed_items_for_output]
        original_ser_nos = [item[1] for item in processed_items_for_output]
        original_full_filenames_processed = [item[2] for item in processed_items_for_output]
        
        # Remove duplicates but keep track of what we're removing
        final_reg_nos = list(dict.fromkeys(original_reg_nos))
        final_ser_nos = list(dict.fromkeys(original_ser_nos))
        final_full_filenames = list(dict.fromkeys(original_full_filenames_processed))
    
        # Find the longest mark_text
        longest_mark_text = max(all_mark_texts, key=len) if all_mark_texts else None

        if not final_reg_nos: # If all entries were removed or skipped
            print(f"All reg_no entries for trademark_data {trademark_data} were removed or skipped. Signaling to remove entire trademark entry.")
            return True, {} # Signal to remove this trademark entry entirely
        
        # Prepare updated trademark data with only the successfully processed/kept items
        updated_data = trademark_data.copy()
        updated_data['reg_no'] = final_reg_nos
        updated_data['ser_no'] = final_ser_nos
        updated_data['full_filename'] = final_full_filenames
        if longest_mark_text:
            updated_data['trademark_text'] = longest_mark_text
        else:
            updated_data['trademark_text'] = "None"

        if final_reg_nos != original_reg_nos:
            self.log_change(
                case_id, plaintiff_id, 'reg_no_changes',
                str(original_reg_nos), str(final_reg_nos),
                f"Changed from {len(original_reg_nos)} to {len(final_reg_nos)} reg_nos"
            )
        
        if final_ser_nos and any(ser_no for ser_no in final_ser_nos):
            self.log_change(
                case_id, plaintiff_id, 'ser_no_additions',
                'None', str(final_ser_nos),
                f"Added {len([s for s in final_ser_nos if s])} ser_nos"
            )
            self.changes_summary['database_updates_needed'] += len([s for s in final_ser_nos if s])
        
        if final_full_filenames != original_full_filenames:
            self.log_change(
                case_id, plaintiff_id, 'filename_changes',
                str(original_full_filenames), str(final_full_filenames),
                f"Filename changes: {len(final_full_filenames)} files"
            )
            self.changes_summary['total_files_to_copy'] += len(final_full_filenames)
        
        if longest_mark_text:
            self.log_change(
                case_id, plaintiff_id, 'mark_text_additions',
                trademark_data.get('trademark_text', 'None'), longest_mark_text,
                f"Added mark text: {longest_mark_text[:50]}..."
            )
        
        # Count removals
        removed_count = len(original_full_filenames) - len(final_full_filenames)
        if removed_count > 0:
            self.log_change(
                case_id, plaintiff_id, 'trademark_removals',
                str(len(original_full_filenames)), str(len(final_full_filenames)),
                f"Removed {removed_count} trademarks"
            )
        
        # Count dead trademarks
        dead_count = len([f for f in final_full_filenames if '_dead_full' in f])
        if dead_count > 0:
            self.log_change(
                case_id, plaintiff_id, 'dead_trademark_markings',
                '0', str(dead_count),
                f"Marked {dead_count} trademarks as dead"
            )

        return True, updated_data

    async def migrate_case_trademarks(self, case_row: pd.Series) -> bool:
        """Migrate all trademarks for a single case"""
        case_id = case_row['id']
        plaintiff_id = int(float(case_row['plaintiff_id']))
        images_data = case_row.get('images', {})
        trademarks_data = images_data.get('trademarks', {})

        if not trademarks_data:
            return True  # No trademarks to migrate

        updated_trademarks = {}
        files_to_copy = []  # List of (old_filename, new_filename) tuples

        print("*"*100)
        print("Started migration for case_id:", case_id)
        for old_filename, trademark_data in trademarks_data.items():
            # Check if already migrated
            if self.is_already_migrated(old_filename):
                updated_trademarks[old_filename] = trademark_data
                self.skipped_count += 1
                continue

            # Process this trademark entry
            success, updated_data = await self.process_trademark_entry(trademark_data, plaintiff_id, case_id)
 
            if not success:
                # Keep original data if processing failed
                updated_trademarks[old_filename] = trademark_data
                self.error_count += 1
                continue
            
            if not updated_data: # User chose to remove trademark
                print(f"Removing trademark {old_filename} from case_id {case_id}")
                self.processed_count += 1 # Count as processed, but removed
                continue
 
            # Generate new filenames based on ser_no
            # The main filename (key in trademarks_data) takes any of the ser_no, using the first one.
            if not updated_data.get('ser_no'):
                print(f"No ser_no found in updated_data for {old_filename}")
                updated_trademarks[old_filename] = trademark_data
                self.error_count += 1
                continue

            primary_ser_no = updated_data['ser_no'][0]
            new_filename_key = f"{primary_ser_no}.webp" # This will be the new key in updated_trademarks

            # Add the main filename to files to copy
            files_to_copy.append((old_filename, new_filename_key))

            # Handle full_filename(s)
            original_full_filenames = trademark_data.get('full_filename', [])
            new_full_filenames = updated_data.get('full_filename', [])

            if len(original_full_filenames) == len(new_full_filenames):
                for i in range(len(original_full_filenames)):
                    old_full = original_full_filenames[i]
                    new_full = new_full_filenames[i]
                    if old_full and new_full:
                        files_to_copy.append((old_full, new_full))
            else:
                print(f"Mismatched original and new full_filenames for {old_filename}. Skipping full_filename copies.")

            # Store with new filename as key
            updated_trademarks[new_filename_key] = updated_data
            self.processed_count += 1

        # Copy all files but 1 by 1, because if a file is not there, we reprocess the case!
        for old_filename, new_filename in files_to_copy:
            copy_result = await self.copy_file_to_new_name(old_filename, new_filename, plaintiff_id, case_id)
            if not copy_result:
                print(f"Stopping further file copies for case {case_id} as one has failed and it's marked for reprocessing.")
                break

        # Update the case data
        case_row['images']['trademarks'] = updated_trademarks

        # At the end, track if this case had changes
        original_trademarks_data = case_row.get('images', {}).get('trademarks', {})
        
        # After updating case_row['images']['trademarks'] = updated_trademarks
        if json.dumps(original_trademarks_data, sort_keys=True) != json.dumps(updated_trademarks, sort_keys=True):
            self.changes_summary['cases_with_changes'] += 1
            self.changes_summary['total_trademarks_changed'] += len(updated_trademarks)
            
            # Log the overall case change
            self.log_change(
                case_id, plaintiff_id, 'case_trademark_update',
                f"{len(original_trademarks_data)} trademarks", 
                f"{len(updated_trademarks)} trademarks",
                f"Case updated with trademark changes"
            )

        return True

    async def migrate_all_cases(self, df: pd.DataFrame) -> pd.DataFrame:
        """Migrate all cases in the dataframe"""
        print(f"Starting migration of {len(df)} cases")
        self.changes_summary['total_cases_processed'] = len(df)

        for i, (index, row) in enumerate(df.iterrows()):
            case_id = row.get('id', 'unknown')
            try:
                original_trademarks_data = row.get('images', {}).get('trademarks', {})
                deep_copy_original_trademarks = copy.deepcopy(original_trademarks_data)

                await self.migrate_case_trademarks(row)

                updated_trademarks_data = row.get('images', {}).get('trademarks', {})

                # Compare original and updated data
                if json.dumps(deep_copy_original_trademarks, sort_keys=True) != json.dumps(updated_trademarks_data, sort_keys=True):
                    # Update the dataframe with the processed row
                    df.loc[index] = row
                    # Save the updated row back to the database immediately
                    # insert_and_update_df_to_GZ_batch(df.loc[[index]], "tb_case", key_column="id")
                    print("-"*100)
                    print(f"Updated DB for case_id {case_id}: changes detected.")
                    print("*"*100)
                else:
                    print(f"Skipping DB update for case_id {case_id}: no changes detected in trademarks data.")

                if (i + 1) % 10 == 0:
                    print(f"\033[91mProcessed {i + 1}/{len(df)} cases\033[0m")
                    self.save_changes_log()
                    self.save_changes_summary()
                    # Save user answers cache
                    # if self.user_answers_cache:
                        # self._save_user_answers_cache()
            except Exception as e:
                print(f"Error processing for case_id {case_id}: {e}, traceback: {traceback.format_exc()}")
                self.error_count += 1

        self.save_changes_log()
        self.save_changes_summary()
        self.print_changes_summary()

        print(f"Migration completed. Processed: {self.processed_count}, Skipped: {self.skipped_count}, Errors: {self.error_count}")
        return df


def main():
    """Main migration function with console logging to file"""
    
    # Start logging to file
    tee = TeeOutput(LOG_FILE)
    original_stdout = sys.stdout
    sys.stdout = tee
    
    try:
        print("Starting Trademark Filename Migration")
        print(f"Console output will be saved to: {LOG_FILE}")
        print("=" * 50)

        # Load cases with trademark images
        print("Loading cases from database...")
        df = get_table_from_GZ("tb_case")

        # Filter cases that have trademark images
        cases_with_trademarks = []
        # df = df[df["id"]==5647]  # Uncomment to test specific case
        # sort df by Date_Filed
        df = df.sort_values(by='date_filed', ascending=True)
        
        for index, row in df.iterrows():
            images_data = row.get('images', {})
            if images_data and 'trademarks' in images_data and images_data['trademarks']:
                cases_with_trademarks.append(index)

        if not cases_with_trademarks:
            print("No cases with trademark images found.")
            return

        df_to_migrate = df.loc[cases_with_trademarks].copy()
        print(f"Found {len(df_to_migrate)} cases with trademark images")

        # Confirm migration (uncomment if you want user confirmation)
        # response = input(f"Proceed with migration of {len(df_to_migrate)} cases? (y/N): ")
        # if response.lower() != 'y':
        #     print("Migration cancelled.")
        #     return

        # Perform migration and reprocessing
        async def run_all():
            async with TrademarkFilenameMigrator() as migrator:
                print(f"Starting migration process at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                await migrator.migrate_all_cases(df_to_migrate)
                
                cases_to_reprocess_list = migrator.get_cases_to_reprocess()

                if cases_to_reprocess_list:
                    print(f"\n{'='*50}")
                    print(f"Starting reprocessing for {len(cases_to_reprocess_list)} failed cases...")
                    print(f"Cases to reprocess: {cases_to_reprocess_list}")
                    print(f"{'='*50}")
                    
                    processing_options = {
                        'update_steps': True,
                        'process_pictures': True,
                        'upload_files_nas': False,
                        'upload_files_cos': True,
                        'run_plaintiff_overview': True,
                        'run_summary_translation': True,
                        'run_step_translation': True,
                        'save_to_db': True,
                        'processing_mode': 'full_reprocess',
                        'refresh_days_threshold': 2000
                    }
                    
                    # Use dataframes from migrator if they exist to avoid reloading
                    if migrator.cases_df is None:
                        print("Loading cases dataframe for reprocessing...")
                        migrator.cases_df = get_table_from_GZ("tb_case", force_refresh=False)
                    if migrator.plaintiffs_df is None:
                        print("Loading plaintiffs dataframe for reprocessing...")
                        migrator.plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=False)

                    await reprocess_cases(
                        cases_to_reprocess=cases_to_reprocess_list,
                        processing_options=processing_options,
                        trace_name="Migrate Trademark Reprocess",
                        full_cases_df=migrator.cases_df,
                        plaintiff_df=migrator.plaintiffs_df
                    )
                    
                    # Clear the reprocessing file after successful reprocessing
                    if os.path.exists(CASES_TO_REPROCESS_FILE):
                        os.remove(CASES_TO_REPROCESS_FILE)
                        print("Cleared the cases_to_reprocess.csv file.")
                else:
                    print("No cases needed reprocessing.")

        # Run the async migration and reprocessing
        print(f"Starting async execution at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        asyncio.run(run_all())

        print(f"Migration and reprocessing completed successfully at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}!")
        print(f"Total execution time: {datetime.now() - datetime.strptime(LOG_FILE.split('_')[-2] + '_' + LOG_FILE.split('_')[-1].replace('.txt', ''), '%Y%m%d_%H%M%S')}")

    except KeyboardInterrupt:
        print(f"\n{'='*50}")
        print("Migration interrupted by user (Ctrl+C)")
        print(f"{'='*50}")
        print("Partial results may be available in log files.")
        
    except Exception as e:
        print(f"\n{'='*50}")
        print(f"ERROR: Migration failed with exception")
        print(f"{'='*50}")
        print(f"Error: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")
        
    finally:
        # Restore original stdout and close log file
        print(f"\n{'='*50}")
        print("LOGGING SUMMARY")
        print(f"{'='*50}")
        
        # Get log file size before closing
        try:
            log_size = os.path.getsize(LOG_FILE) if os.path.exists(LOG_FILE) else 0
            print(f"Log file size: {log_size:,} bytes")
        except:
            print("Could not determine log file size")
            
        sys.stdout = original_stdout
        tee.close()
        
        # Print final message to console only (after restoring stdout)
        print(f"Console log saved to: {LOG_FILE}")
        print(f"Migration completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
