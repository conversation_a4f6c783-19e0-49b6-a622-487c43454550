import asyncio
import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ
from Alerts.PicturesProcessing.migrate_trademark_filenames import TrademarkFilenameMigrator, main as run_full_migration

async def test_specific_cases(case_ids_to_test: list[int]):
    """
    Runs the trademark filename migration for a specific list of case IDs.
    """
    print(f"Starting Trademark Filename Migration for {len(case_ids_to_test)} specific cases.")
    print("=" * 50)

    # Load all cases from the database
    print("Loading all cases from database to filter...")
    df_all_cases = get_table_from_GZ("tb_case", force_refresh=False)
    if df_all_cases.empty:
        print("Could not retrieve case data from the database.")
        return

    # Filter for the specific cases to test
    df_to_migrate = df_all_cases[df_all_cases['id'].isin(case_ids_to_test)].copy()

    if df_to_migrate.empty:
        print(f"None of the specified case IDs {case_ids_to_test} were found in the database.")
        return

    print(f"Found {len(df_to_migrate)} cases to test: {df_to_migrate['id'].tolist()}")

    # Perform migration using the existing migrator class
    async with TrademarkFilenameMigrator() as migrator:
        await migrator.migrate_all_cases(df_to_migrate)

    print("\nTest migration completed for specified cases.")
    print(f"Summary: Processed: {migrator.processed_count}, Skipped: {migrator.skipped_count}, Errors: {migrator.error_count}")

    # Note: This test script does not automatically run the reprocessing step.
    # You can check the 'cases_to_reprocess.csv' file to see if any cases were flagged.

if __name__ == "__main__":
    # --- Configuration ---
    # Define the list of case IDs you want to test here
    # For example: [123, 456, 789]
    SPECIFIC_CASE_IDS = [14606]
    # --- End Configuration ---

    if not SPECIFIC_CASE_IDS:
        print("The SPECIFIC_CASE_IDS list is empty. Please add case IDs to test.")
        print("If you want to run the full migration, please run the original 'migrate_trademark_filenames.py' script.")
    else:
        asyncio.run(test_specific_cases(SPECIFIC_CASE_IDS))